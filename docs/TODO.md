# EmailConnect App - TODO List

This document organizes all identified improvements and fixes by priority level.

## High Priority (h) - Critical Features & Fixes

### Data Retention Implementation
- **Issue**: Need to implement automatic data retention for GDPR compliance
- **Requirements**: 
  - 2 hours retention for free users
  - 24 hours retention for Pro users  
  - User-configurable via RetentionSection.vue in SettingsPage.vue
  - Store settings in new user_settings table column
- **Impact**: Critical for GDPR compliance and preventing database bloat

### Email Payload Viewer
- **Issue**: Need modal to view webhookPayload JSON for log entries
- **Requirements**:
  - Modal with proper JSON viewer component
  - Accessible from LogsView.vue for entries with webhookPayload
  - Clean, formatted JSON display
- **Impact**: Essential for debugging and user transparency

### Spam Filtering UI Simplification
- **Issue**: Current spam filtering UI is overly complex
- **Requirements**:
  - Focus only on 'block threshold' setting
  - Remove pass/review zone options from UI
  - Simplify UX to single threshold management
  - Backend should remain unchanged (threshold in configuration)
- **Impact**: Reduces user confusion, focuses on actionable setting

## Medium Priority (m) - Important Improvements

### Notification System Fix
- **Issue**: Notification bell never shows notifications, dropdown empty, Postgres notifications table empty
- **Requirements**:
  - Debug notification creation and delivery
  - Fix WebSocket event handling for real-time updates
  - Ensure toast notifications work with bell updates
- **Impact**: Breaks user communication system

### EC Storage Folder Structure
- **Issue**: Need to define folder structure for EmailConnect's S3 storage
- **Requirements**:
  - Decide on folder naming: userId/aliasId pattern
  - Implement folder creation and validation
  - Handle AWS SDK errors for permissions/naming issues
- **Impact**: Required for Pro storage feature

### Database Cleanup
- **Issue**: Unused columns and tokens in database
- **Requirements**:
  - Clean up postfix.db unused columns (domains.destination, aliases fields)
  - Review verificationToken usage in domains table
  - Remove if no longer needed
- **Impact**: Reduces technical debt and confusion

### Payment Implementation
- **Issue**: Missing core payment functionality
- **Requirements**:
  - Implement subscription creation flow
  - Implement credit purchasing system
  - Integration with existing Mollie setup
- **Impact**: Required for monetization

### Plan Configuration Centralization
- **Issue**: Hardcoded plan logic in main.go
- **Requirements**:
  - Move to environment variable like PLANS_WITH_SPAM_FILTERING=pro,enterprise
  - Centralize plan configuration logic
  - Maintain consistency with plan-config.service.ts
- **Impact**: Improves maintainability

## Low Priority (l) - Nice to Have

### LogsView Create CTA
- **Issue**: Add Create Domain CTA to LogsView TabNavigation
- **Requirements**:
  - Show Create CTA in TabNavigation component
  - Set default action to create domain
- **Impact**: Minor UX improvement

### Schema Validation Tooling
- **Issue**: Need better API schema validation
- **Requirements**:
  - Automated tests for API schemas vs actual data structures
  - Schema validation warnings for stripped properties
  - Schema drift detection in CI/CD
- **Impact**: Improves API reliability

### S3 Storage Error Handling
- **Issue**: Need proper error handling for user's S3-compatible storage
- **Requirements**:
  - Catch AWS SDK errors for bucket creation
  - Validate folder names (no symbols, duplicates, etc.)
  - Return proper error messages to user
- **Impact**: Better user experience for storage configuration

### Environment Variable Cleanup
- **Issue**: Hardcoded plan logic should use environment variables
- **Requirements**:
  - Create PLANS_WITH_SPAM_FILTERING environment variable
  - Update main.go to use environment configuration
  - Document new environment variables
- **Impact**: Improves configuration management

---

## Implementation Notes

### Database Schema Changes Needed
- Add user_settings table column for retention settings
- Review and potentially remove unused columns in postfix.db
- Verify verificationToken usage in domains table

### Frontend Components to Create/Modify
- RetentionSection.vue for SettingsPage.vue
- JSON viewer modal for webhook payloads
- Simplified spam filtering UI in DomainForm.vue

### Backend Services to Implement
- Data retention cleanup service
- Enhanced notification creation and delivery
- Payment processing endpoints

### Testing Requirements
- Test data retention cleanup
- Test notification system end-to-end
- Test simplified spam filtering configuration
- Test payment flows when implemented
