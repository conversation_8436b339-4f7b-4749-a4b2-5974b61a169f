<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title mb-6">Profile information</h2>
      <div class="bg-base-200/40 rounded-lg p-6">
        <div class="space-y-4">
          <div>
            <label class="label">
              <span class="label-text">Display name</span>
            </label>
            <input
              type="text"
              v-model="displayName"
              placeholder="Enter your display name"
              class="input input-bordered w-full"
            >
          </div>
          <div>
            <label class="label">
              <span class="label-text">Email address</span>
            </label>
            <div class="join w-full">
              <input
                type="email"
                :value="currentUser?.email || ''"
                disabled
                class="input join-item w-full cursor-not-allowed"
                placeholder="Email address"
              />
              <button
                type="button"
                @click="toggleEmailForm"
                class="btn join-item cursor-pointer"
              >
                Change
              </button>
            </div>
            <label class="label">
              <span class="label-text-alt text-xs">Click "Change" to update your email address.</span>
            </label>
          </div>
          <div v-if="showEmailForm" class="mt-4 p-4 border border-base-300 rounded-lg bg-base-100">
            <h4 class="font-medium mb-3">Change email address</h4>
            <div class="space-y-3">
              <div>
                <label class="label">
                  <span class="label-text">New email address</span>
                </label>
                <input
                  type="email"
                  v-model="newEmail"
                  placeholder="Enter new email address"
                  class="input input-bordered w-full"
                >
              </div>
              <div>
                <label class="label">
                  <span class="label-text">Current password</span>
                </label>
                <input
                  type="password"
                  v-model="emailChangePassword"
                  placeholder="Enter your current password"
                  class="input input-bordered w-full"
                >
              </div>
              <div class="flex gap-2">
                <button
                  type="button"
                  @click="changeEmail"
                  :disabled="changingEmail"
                  class="btn btn-primary"
                >
                  {{ changingEmail ? 'Changing...' : 'Change email' }}
                </button>
                <button
                  type="button"
                  @click="cancelEmailChange"
                  class="btn btn-ghost"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
          <div class="mt-6">
            <label class="label">
              <span class="label-text">Password</span>
            </label>
            <div class="join w-full">
              <input
                type="password"
                value="••••••••••••"
                disabled
                class="input join-item w-full cursor-not-allowed"
                placeholder="Password"
              />
              <button
                type="button"
                @click="togglePasswordForm"
                class="btn join-item cursor-pointer"
              >
                Change
              </button>
            </div>
          </div>
          <div v-if="showPasswordForm" class="mt-4 p-4 border border-base-300 rounded-lg bg-base-100">
            <h4 class="font-medium mb-3">Change password</h4>
            <div class="space-y-3">
              <div>
                <label class="label">
                  <span class="label-text">Current password</span>
                </label>
                <input
                  type="password"
                  v-model="currentPassword"
                  placeholder="Enter your current password"
                  class="input input-bordered w-full"
                >
              </div>
              <div>
                <label class="label">
                  <span class="label-text">New password</span>
                </label>
                <input
                  type="password"
                  v-model="newPassword"
                  placeholder="Enter new password (min 6 characters)"
                  class="input input-bordered w-full"
                >
              </div>
              <div>
                <label class="label">
                  <span class="label-text">Confirm new password</span>
                </label>
                <input
                  type="password"
                  v-model="confirmPassword"
                  placeholder="Confirm new password"
                  class="input input-bordered w-full"
                >
              </div>
              <div class="flex gap-2">
                <button
                  type="button"
                  @click="changePassword"
                  :disabled="changingPassword"
                  class="btn btn-primary"
                >
                  {{ changingPassword ? 'Changing...' : 'Change password' }}
                </button>
                <button
                  type="button"
                  @click="cancelPasswordChange"
                  class="btn btn-ghost"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
          <div class="pt-4">
            <button
              type="button"
              @click="saveChanges"
              :disabled="saving"
              class="btn btn-primary"
            >
              {{ saving ? 'Saving...' : 'Save changes' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMetrics } from '../../composables/useMetrics'

// State
const displayName = ref('')
const currentPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const newEmail = ref('')
const emailChangePassword = ref('')
const saving = ref(false)
const changingPassword = ref(false)
const changingEmail = ref(false)
const showPasswordForm = ref(false)
const showEmailForm = ref(false)

// User
const { metricsData, loadMetrics } = useMetrics()
const user = ref<any>(null)
const currentUser = computed(() => user.value || metricsData.value?.user || null)

// Load user profile
const loadUserProfile = async () => {
  try {
    await loadMetrics()
    if (metricsData.value?.user) {
      user.value = metricsData.value.user
      displayName.value = metricsData.value.user.name || ''
      newEmail.value = metricsData.value.user.email
    }
  } catch (error) {
    console.error('Failed to load user profile:', error)
  }
}

// Save display name
const saveChanges = async () => {
  saving.value = true
  try {
    const response = await fetch('/api/profile', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({ name: displayName.value || null })
    })
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || 'Failed to update profile')
    }
    const data = await response.json()
    if (data.success && data.user) {
      user.value = { ...user.value, ...data.user }
      alert('Profile updated successfully!')
    }
  } catch (error: any) {
    console.error('Failed to save changes:', error)
    alert(error.message || 'Failed to save changes. Please try again.')
  } finally {
    saving.value = false
  }
}

// Change password
const changePassword = async () => {
  if (newPassword.value !== confirmPassword.value) {
    alert('New passwords do not match')
    return
  }
  if (newPassword.value.length < 6) {
    alert('New password must be at least 6 characters long')
    return
  }
  changingPassword.value = true
  try {
    const response = await fetch('/api/profile/password', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({
        currentPassword: currentPassword.value,
        newPassword: newPassword.value
      })
    })
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || 'Failed to change password')
    }
    currentPassword.value = ''
    newPassword.value = ''
    confirmPassword.value = ''
    showPasswordForm.value = false
    alert('Password changed successfully!')
  } catch (error: any) {
    console.error('Failed to change password:', error)
    alert(error.message || 'Failed to change password. Please try again.')
  } finally {
    changingPassword.value = false
  }
}

// Change email
const changeEmail = async () => {
  if (!newEmail.value || !emailChangePassword.value) {
    alert('Please fill in all fields')
    return
  }
  changingEmail.value = true
  try {
    const response = await fetch('/api/profile/email', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({
        newEmail: newEmail.value,
        password: emailChangePassword.value
      })
    })
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || 'Failed to change email')
    }
    const data = await response.json()
    if (data.success && data.user) {
      user.value = { ...user.value, ...data.user }
      emailChangePassword.value = ''
      showEmailForm.value = false
      alert('Email address changed successfully!')
    }
  } catch (error: any) {
    console.error('Failed to change email:', error)
    alert(error.message || 'Failed to change email. Please try again.')
  } finally {
    changingEmail.value = false
  }
}

function toggleEmailForm() {
  showEmailForm.value = !showEmailForm.value
}
function cancelEmailChange() {
  showEmailForm.value = false
  emailChangePassword.value = ''
}
function togglePasswordForm() {
  showPasswordForm.value = !showPasswordForm.value
}
function cancelPasswordChange() {
  showPasswordForm.value = false
  currentPassword.value = ''
  newPassword.value = ''
  confirmPassword.value = ''
}

onMounted(() => {
  loadUserProfile()
})
</script>
