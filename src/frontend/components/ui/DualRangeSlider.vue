<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  min?: number
  max?: number
  step?: number
  greenValue?: number
  redValue?: number
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  min: 0,
  max: 15,
  step: 0.5,
  greenValue: 2.0,
  redValue: 5.0,
  disabled: false
})

const emit = defineEmits<{
  'update:greenValue': [value: number]
  'update:redValue': [value: number]
}>()

// Internal values
const greenSlider = ref(props.greenValue)
const redSlider = ref(props.redValue)

// Watch for prop changes
watch(() => props.greenValue, (newVal) => {
  greenSlider.value = newVal
})

watch(() => props.redValue, (newVal) => {
  redSlider.value = newVal
})

// Handle green slider change
const updateGreen = (event: Event) => {
  const value = parseFloat((event.target as HTMLInputElement).value)
  const rounded = parseFloat(value.toFixed(1))

  // Ensure green is always less than red
  if (rounded >= redSlider.value) {
    redSlider.value = Math.min(props.max, rounded + props.step)
    emit('update:redValue', redSlider.value)
  }

  greenSlider.value = rounded
  emit('update:greenValue', rounded)
}

// Handle red slider change
const updateRed = (event: Event) => {
  const value = parseFloat((event.target as HTMLInputElement).value)
  const rounded = parseFloat(value.toFixed(1))

  // Ensure red is always greater than green
  if (rounded <= greenSlider.value) {
    greenSlider.value = Math.max(props.min, rounded - props.step)
    emit('update:greenValue', greenSlider.value)
  }

  redSlider.value = rounded
  emit('update:redValue', rounded)
}
</script>

<template>
  <div class="space-y-4">
    <!-- Pass Threshold -->
    <div class="space-y-2">
      <div class="flex items-center justify-between">
        <label class="text-sm font-medium flex items-center gap-2">
          <div class="w-3 h-3 bg-success rounded-full"></div>
          Pass threshold
        </label>
        <span class="text-sm font-mono">≤ {{ greenSlider }}</span>
      </div>
      <input
        type="range"
        :min="min"
        :max="max"
        :step="step"
        :value="greenSlider"
        @input="updateGreen"
        :disabled="disabled"
        class="range range-success range-sm w-full"
      />
      <div class="text-xs text-base-content/60">
        Emails with spam scores at or below this value pass through normally
      </div>
    </div>

    <!-- Block Threshold -->
    <div class="space-y-2">
      <div class="flex items-center justify-between">
        <label class="text-sm font-medium flex items-center gap-2">
          <div class="w-3 h-3 bg-error rounded-full"></div>
          Block threshold
        </label>
        <span class="text-sm font-mono">≥ {{ redSlider }}</span>
      </div>
      <input
        type="range"
        :min="min"
        :max="max"
        :step="step"
        :value="redSlider"
        @input="updateRed"
        :disabled="disabled"
        class="range range-error range-sm w-full"
      />
      <div class="text-xs text-base-content/60">
        Emails with spam scores at or above this value are blocked
      </div>
    </div>

    <!-- Summary -->
    <div class="bg-base-200/50 rounded-lg p-3 text-sm">
      <div class="flex items-center gap-2 mb-2">
        <div class="w-3 h-3 bg-warning rounded-full"></div>
        <span class="font-medium">Review zone: {{ greenSlider }} - {{ redSlider }}</span>
      </div>
      <div class="text-xs text-base-content/70">
        Emails in this range are flagged for manual review in webhook payloads
      </div>
    </div>
  </div>
</template>
