<script setup lang="ts">
import { ref, watch } from 'vue'

interface Props {
  min?: number
  max?: number
  step?: number
  value?: number
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  min: 1.0,
  max: 15.0,
  step: 0.5,
  value: 10.0,
  disabled: false
})

const emit = defineEmits<{
  'update:value': [value: number]
}>()

// Internal value
const sliderValue = ref(props.value)

// Watch for prop changes
watch(() => props.value, (newVal) => {
  sliderValue.value = newVal
})

// Handle slider change
const updateValue = (event: Event) => {
  const value = parseFloat((event.target as HTMLInputElement).value)
  const rounded = parseFloat(value.toFixed(1))
  
  sliderValue.value = rounded
  emit('update:value', rounded)
}
</script>

<template>
  <div class="space-y-4">
    <div class="space-y-2">
      <div class="flex items-center justify-between">
        <label class="text-sm font-medium flex items-center gap-2">
          <div class="w-3 h-3 bg-error rounded-full"></div>
          Block threshold
        </label>
        <span class="text-sm font-mono">≥ {{ sliderValue }}</span>
      </div>
      <input
        type="range"
        :min="min"
        :max="max"
        :step="step"
        :value="sliderValue"
        @input="updateValue"
        :disabled="disabled"
        class="range range-error range-sm w-full"
      />
      <div class="text-xs text-base-content/60">
        Emails with spam scores at or above this value are blocked
      </div>
    </div>
  </div>
</template>
