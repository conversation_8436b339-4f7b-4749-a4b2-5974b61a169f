<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useForm } from '../../composables/useForm'
import { useDomainApi } from '../../composables/useApi'
import { useDataRefresh } from '../../composables/useDataRefresh'
import { useMetrics } from '../../composables/useMetrics'
import { usePermissions } from '../../composables/usePermissions'
import WebhookSelector from './WebhookSelector.vue'
import BlockThresholdSlider from '../ui/BlockThresholdSlider.vue'
import type { CreateDomainRequest } from '../../types'

interface Props {
  initialData?: Partial<CreateDomainRequest & {
    preselectedWebhook?: string
    id?: string
    webhookId?: string
    spamFiltering?: boolean
    configuration?: any
  }>
  isEditMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  isEditMode: false
})

const emit = defineEmits<{
  success: [domain: any]
  cancel: []
}>()

// Form setup
const { values, errors, isSubmitting, isValid, setFieldValue, handleSubmit } = useForm<CreateDomainRequest>({
  domain: props.initialData.domain || '',
  webhookId: props.initialData.preselectedWebhook || props.initialData.webhookId || '',
  createCatchAll: props.isEditMode ? false : true, // Only for new domains
}, [
  { name: 'domain', label: 'Domain', type: 'text', required: true },
  { name: 'webhookId', label: 'Webhook', type: 'select', required: true }
])

// API setup
const { createDomain, updateDomain, updateSpamFilter } = useDomainApi()
const { refreshWithSuccess } = useDataRefresh()
const { refreshMetrics } = useMetrics()
const { hasPermission, loadPermissions } = usePermissions()

// Spam filtering state
const spamFilteringEnabled = ref(false)
const blockThreshold = ref(5.0)

// Initialize spam filtering state and thresholds from configuration
console.log('DomainForm initialData:', props.initialData)
console.log('DomainForm configuration:', props.initialData.configuration)
console.log('DomainForm spamFiltering field:', props.initialData.spamFiltering)

if (props.initialData.configuration?.spamFiltering) {
  const spamConfig = props.initialData.configuration.spamFiltering
  console.log('Loading spam config from configuration:', spamConfig)
  spamFilteringEnabled.value = spamConfig.enabled || false

  if (spamConfig.thresholds) {
    // Use the red/block threshold as the single threshold
    blockThreshold.value = spamConfig.thresholds.red || spamConfig.thresholds.block || 5.0
  }
} else if (props.initialData.spamFiltering !== undefined) {
  // Fallback to legacy spamFiltering boolean field
  console.log('Loading spam filtering from legacy field:', props.initialData.spamFiltering)
  spamFilteringEnabled.value = props.initialData.spamFiltering
}

console.log('Final spam filtering state:', {
  enabled: spamFilteringEnabled.value,
  blockThreshold: blockThreshold.value
})

// Check if user can use spam filtering (Pro+ feature)
const canUseSpamFiltering = computed(() => {
  return hasPermission('spam_filtering')
})

// Methods
const onWebhookCreated = () => {
  // The WebhookSelector component will automatically select the new webhook
}

const onSubmit = async () => {
  await handleSubmit(async (formData) => {
    let result: any

    if (props.isEditMode && props.initialData.id) {
      // Edit mode - update existing domain
      const updateData: any = {
        webhookId: formData.webhookId
      }

      // Update domain (webhook only)
      result = await updateDomain(props.initialData.id, updateData)

      // Update spam filtering separately (if user has permission)
      if (canUseSpamFiltering.value) {
        try {
          await updateSpamFilter(props.initialData.id, {
            enabled: spamFilteringEnabled.value,
            thresholds: {
              green: 0, // Always pass emails below block threshold
              red: blockThreshold.value // Block threshold
            }
          })
        } catch (spamError: any) {
          console.warn('Failed to update spam filtering:', spamError.message)
          // Don't fail the entire update if spam filtering fails
        }
      }

      emit('success', result.domain)
      refreshWithSuccess('Domain updated successfully!', 'domains')
    } else {
      // Create mode - create new domain
      const createData: any = { ...formData }

      // Always include spam filtering configuration (save state even if disabled)
      createData.configuration = {
        spamFiltering: {
          enabled: spamFilteringEnabled.value && canUseSpamFiltering.value, // Only enable if user has permission
          thresholds: {
            green: 0, // Always pass emails below block threshold
            red: blockThreshold.value // Block threshold
          }
        }
      }

      result = await createDomain(createData) as any
      emit('success', result.domain)

      // Update data reactively - refresh both domains and aliases if catch-all was created
      refreshWithSuccess('Domain created successfully!', 'domains')

      // If catch-all alias was created, also refresh aliases count
      if (formData.createCatchAll && result.alias) {
        const { triggerRefresh } = useDataRefresh()
        triggerRefresh('aliases')
      }

      // Auto-open verification steps modal for new domains
      setTimeout(() => {
        const modalData: any = {
          domain: result.domain.domain,
          domainId: result.domain.id,
          verificationToken: result.domain.verificationToken,
          webhookVerified: result.webhook?.verified
        };

        // Include webhook verification data if webhook needs verification
        if (result.webhook && !result.webhook.verified && result.webhook.id) {
          modalData.webhookNeedsVerification = true;
          modalData.webhookId = result.webhook.id;
          modalData.webhookUrl = result.webhook.url;
          modalData.webhookName = result.webhook.name;
        }

        (window as any).openModal('domain-verification', modalData);
      }, 500);
    }

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
      } catch (error) {
        console.error('Failed to refresh metrics after domain operation:', error)
      }
    }, 500)
  })
}

onMounted(async () => {
  // Load user permissions to check for spam filtering access
  await loadPermissions()
  // WebhookSelector component handles loading webhooks
})
</script>

<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Domain Name -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Domain name</span>
      </label>
      <input
        :value="values.domain"
        @input="setFieldValue('domain', ($event.target as HTMLInputElement).value)"
        type="text"
        autocapitalize="none"
        spellcheck="false"
        inputmode="url"
        placeholder="example.com"
        class="w-full input input-bordered"
        :class="{ 'input-error': errors.domain }"
        :disabled="isEditMode"
        required
      />
      <div v-if="errors.domain" class="label">
        <span class="label-text-alt text-error">{{ errors.domain }}</span>
      </div>
      <div class="label">
        <span class="label-text-alt text-xs">
          {{ isEditMode ? 'Domain name cannot be changed' : 'Enter your domain without \'www\' prefix' }}
        </span>
      </div>
    </div>

    <!-- Webhook Selection -->
    <WebhookSelector
      :model-value="values.webhookId"
      @update:model-value="setFieldValue('webhookId', $event)"
      @webhook-created="onWebhookCreated"
      context="create-domain"
      required
    />
    <div v-if="errors.webhookId" class="label">
      <span class="label-text-alt text-error">{{ errors.webhookId }}</span>
    </div>

    <!-- Spam Filtering (Pro+ Feature) -->
    <div class="form-control">
      <div class="p-4 bg-base-200/40 rounded-lg border border-base-300 space-y-4">
        <!-- Enable/Disable Toggle -->
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="font-medium">
              <span class="badge badge-primary badge-outline badge-sm">Pro</span>
              Enable spam filtering
            </div>
            <div class="text-sm text-base-content/60">
              Process emails through SpamAssassin with custom thresholds
            </div>
          </div>
          <div class="form-control">
            <label class="label cursor-pointer">
              <input
                type="checkbox"
                :disabled="!canUseSpamFiltering"
                v-model="spamFilteringEnabled"
                class="toggle toggle-primary"
              />
            </label>
          </div>
        </div>

        <!-- Threshold Configuration (only show when enabled) -->
        <div v-if="spamFilteringEnabled" class="pt-4 border-t border-base-300">
          <BlockThresholdSlider
            :min="0"
            :max="15"
            :step="0.5"
            :value="blockThreshold"
            :disabled="!canUseSpamFiltering"
            @update:value="blockThreshold = $event"
          />
        </div>
      </div>
    </div>

    <!-- Form-level errors -->
    <div v-if="errors._form" class="alert alert-error rounded-lg">
      <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>{{ errors._form }}</span>
    </div>

    <!-- Form Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="emit('cancel')"
        class="btn btn-ghost"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="!isValid || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        {{ isSubmitting
          ? (isEditMode ? 'Updating...' : 'Creating...')
          : (isEditMode ? 'Update domain' : 'Create domain')
        }}
      </button>
    </div>
  </form>
</template>